"use client"

import { Card } from '@/components/ui/card';
import { TimeBlock } from '@/lib/types';
import { TimeBlockGrid } from '@/components/time-blocks/time-block-grid';
import { toast } from 'sonner';
import { usePreferences } from '@/hooks/use-preferences';

interface GridViewTabProps {
  selectedDate: Date;
  filteredBlocks: TimeBlock[];
  onAddBlock: () => void;
  onEditBlock: (block: TimeBlock) => void;
}

export function GridViewTab({
  selectedDate,
  filteredBlocks,
  onAddBlock,
  onEditBlock
}: GridViewTabProps) {
  const { preferences } = usePreferences();

  // Create a key that changes when preferences change to force re-render
  const gridKey = `${selectedDate.toISOString()}-${preferences?.timeInterval || '60'}-${preferences?.useCustomTimeBlocks || false}-${JSON.stringify(preferences?.customTimeBlocks || [])}`;

  return (
    <Card className="shadow-sm border-0 overflow-hidden">
      <TimeBlockGrid
        key={gridKey}
        date={selectedDate}
        timeBlocks={filteredBlocks}
        onAddBlock={onAddBlock}
        onEditBlock={onEditBlock}
        onUpdate={(id, data) => {
          // This is a placeholder implementation since we don't have access to updateTimeBlock
          // In a real implementation, this would call the updateTimeBlock function
          console.log('Update time block:', id, data);
          toast.info('Time block update feature is not available in this view');
          return Promise.resolve();
        }}
      />
    </Card>
  );
}
