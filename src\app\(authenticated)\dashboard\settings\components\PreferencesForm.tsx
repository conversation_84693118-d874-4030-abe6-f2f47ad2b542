"use client"

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Card, CardContent } from '@/components/ui/card';
import { toast } from 'sonner';
import { preferencesSchema, PreferencesFormValues } from '../schemas';
import { TimeIntervalPreference } from './preferences/TimeIntervalPreference';
import { TimeDisplayPreference } from './preferences/TimeDisplayPreference';
import { TimeFormatPreference } from './preferences/TimeFormatPreference';
import { SyncPreference } from './preferences/SyncPreference';
import { CustomTimeBlocksPreference } from './preferences/CustomTimeBlocksPreference';
interface PreferencesFormProps {
  initialData: {
    timeInterval: string;
    startHour: string;
    endHour: string;
    timeFormat: string;
    darkMode: boolean;
    syncEnabled?: boolean;
    customTimeBlocks?: Array<{ startTime: string; endTime: string }>;
    useCustomTimeBlocks?: boolean;
  };
  onSavePreferences: (values: PreferencesFormValues) => Promise<boolean>;
}

export function PreferencesForm({ initialData, onSavePreferences }: PreferencesFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formKey, setFormKey] = useState(0); // Force form re-render when needed

  // Helper function to get form values from initialData
  const getFormValues = (data: typeof initialData) => ({
    timeInterval: (['15', '30', '60', '120', '180', '240', '300', '360', '420', '480', '540', '600', '660', '720'] as const).includes(data.timeInterval as any)
      ? (data.timeInterval as '15' | '30' | '60' | '120' | '180' | '240' | '300' | '360' | '420' | '480' | '540' | '600' | '660' | '720')
      : '60',
    startHour: data.startHour || '0',
    endHour: data.endHour || '23',
    timeFormat: (data.timeFormat === '12' ? '12' : '24') as '12' | '24',
    darkMode: data.darkMode || false,
    syncEnabled: data.syncEnabled !== undefined ? data.syncEnabled : false,
    customTimeBlocks: data.customTimeBlocks || [],
    useCustomTimeBlocks: data.useCustomTimeBlocks || false,
  });

  const form = useForm<PreferencesFormValues>({
    resolver: zodResolver(preferencesSchema),
    defaultValues: getFormValues(initialData),
  });

  // Reset form when initialData changes (e.g., after successful save)
  useEffect(() => {
    console.log('PreferencesForm: Resetting form with initialData:', initialData);
    const formData = {
      timeInterval: (['15', '30', '60', '120', '180', '240', '300', '360', '420', '480', '540', '600', '660', '720'] as const).includes(initialData.timeInterval as any)
        ? (initialData.timeInterval as '15' | '30' | '60' | '120' | '180' | '240' | '300' | '360' | '420' | '480' | '540' | '600' | '660' | '720')
        : '60',
      startHour: initialData.startHour || '0',
      endHour: initialData.endHour || '23',
      timeFormat: (initialData.timeFormat === '12' ? '12' : '24') as '12' | '24',
      darkMode: initialData.darkMode || false,
      syncEnabled: initialData.syncEnabled !== undefined ? initialData.syncEnabled : false,
      customTimeBlocks: initialData.customTimeBlocks || [],
      useCustomTimeBlocks: initialData.useCustomTimeBlocks || false,
    };
    console.log('PreferencesForm: Form data being set:', formData);
    form.reset(formData);
    setFormKey(prev => prev + 1); // Force re-render of form components
  }, [initialData, form]);

  const onSubmit = async (values: PreferencesFormValues) => {
    setIsSubmitting(true);

    try {
      console.log('Updating preferences with:', values);

      // Use the provided save function
      const success = await onSavePreferences(values);

      if (success) {
        toast.success('Preferences updated successfully');
      } else {
        throw new Error('Failed to update preferences');
      }
    } catch (error: any) {
      console.error('Preferences update error:', error);
      toast.error(error.message || 'Failed to update preferences');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="shadow-sm">
      <CardContent className="pt-3 px-4">
        <Form {...form}>
          <form key={formKey} onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
            <TimeIntervalPreference form={form} />
            <CustomTimeBlocksPreference form={form} />
            <TimeDisplayPreference form={form} />
            <TimeFormatPreference form={form} />
            <SyncPreference form={form} />

            <div className="flex justify-end pt-1">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-1 h-8 border text-sm"
              >
                {isSubmitting ? 'Saving...' : 'Save Preferences'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
