"use client"

import { useState } from 'react';
import { UseFormReturn, useFieldArray } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent } from '@/components/ui/card';
import { Plus, Trash2 } from 'lucide-react';
import { PreferencesFormValues } from '../../schemas';

interface CustomTimeBlocksPreferenceProps {
  form: UseFormReturn<PreferencesFormValues>;
}

export function CustomTimeBlocksPreference({ form }: CustomTimeBlocksPreferenceProps) {
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "customTimeBlocks",
  });

  const useCustomTimeBlocks = form.watch("useCustomTimeBlocks");

  const addTimeBlock = () => {
    append({ startTime: "09:00", endTime: "10:00" });
  };

  const removeTimeBlock = (index: number) => {
    remove(index);
  };

  return (
    <div className="bg-muted/10 p-3 rounded-md">
      <div className="space-y-4">
        {/* Toggle for using custom time blocks */}
        <FormField
          control={form.control}
          name="useCustomTimeBlocks"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
              <div className="space-y-0.5">
                <FormLabel className="text-sm font-medium">
                  Use Custom Time Blocks
                </FormLabel>
                <FormDescription className="text-xs">
                  Override default time intervals with custom time blocks
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        {/* Custom time blocks section */}
        {useCustomTimeBlocks && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <FormLabel className="text-sm font-medium">Custom Time Blocks</FormLabel>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addTimeBlock}
                className="h-8 px-3"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Block
              </Button>
            </div>

            {fields.length === 0 && (
              <div className="text-center py-4 text-sm text-muted-foreground border-2 border-dashed rounded-md">
                No custom time blocks added. Click "Add Block" to create your first time block.
              </div>
            )}

            <div className="space-y-2">
              {fields.map((field, index) => (
                <Card key={field.id} className="p-3">
                  <CardContent className="p-0">
                    <div className="flex items-center gap-3">
                      <div className="flex-1 grid grid-cols-2 gap-2">
                        <FormField
                          control={form.control}
                          name={`customTimeBlocks.${index}.startTime`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-xs">Start Time</FormLabel>
                              <FormControl>
                                <Input
                                  type="time"
                                  {...field}
                                  className="h-8 text-sm"
                                />
                              </FormControl>
                              <FormMessage className="text-xs" />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`customTimeBlocks.${index}.endTime`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-xs">End Time</FormLabel>
                              <FormControl>
                                <Input
                                  type="time"
                                  {...field}
                                  className="h-8 text-sm"
                                />
                              </FormControl>
                              <FormMessage className="text-xs" />
                            </FormItem>
                          )}
                        />
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeTimeBlock(index)}
                        className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {fields.length > 0 && (
              <FormDescription className="text-xs">
                Custom time blocks will be displayed in the grid view instead of regular intervals.
                Make sure there are no overlapping time periods.
              </FormDescription>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
