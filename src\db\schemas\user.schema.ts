import mongoose from 'mongoose';
import bcrypt from 'bcrypt';

/**
 * User schema
 */
export const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please provide a name'],
    trim: true,
  },
  email: {
    type: String,
    required: [true, 'Please provide an email'],
    unique: true,
    trim: true,
    lowercase: true,
  },
  password: {
    type: String,
    required: [true, 'Please provide a password'],
  },
  preferences: {
    timeInterval: {
      type: String,
      enum: ['15', '30', '60', '120', '180', '240', '300', '360', '420', '480', '540', '600', '660', '720'],
      default: '60',
    },
    startHour: {
      type: String,
      default: '0',
    },
    endHour: {
      type: String,
      default: '23',
    },
    timeFormat: {
      type: String,
      enum: ['12', '24'],
      default: '12',
    },
    darkMode: {
      type: Boolean,
      default: false,
    },
    syncEnabled: {
      type: Boolean,
      default: true,
    },
    emailNotifications: {
      type: <PERSON><PERSON><PERSON>,
      default: true,
    },

  },
}, {
  timestamps: true,
});

/**
 * Pre-save hook for password hashing
 */
userSchema.pre('save', async function(next) {
  // Skip if password is not modified
  if (!this.isModified('password')) {
    return next();
  }

  try {
    // Ensure password is trimmed and valid
    if (typeof this.password === 'string') {
      this.password = this.password.trim();

      // Check if password is empty after trimming
      if (this.password.length === 0) {
        return next(new Error('Password cannot be empty'));
      }

      // Check if password meets minimum length requirement
      if (this.password.length < 8) {
        return next(new Error('Password must be at least 8 characters long'));
      }
    } else {
      return next(new Error('Password must be a string'));
    }

    // Explicitly cast this.password to string to satisfy TypeScript
    const passwordString = String(this.password);

    // Generate salt and hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(passwordString, salt);

    // Verify the hash was generated correctly
    if (!hashedPassword || hashedPassword.length < 10) {
      return next(new Error('Failed to generate valid password hash'));
    }

    // Set the hashed password
    this.password = hashedPassword;
    next();
  } catch (error) {
    next(error as Error);
  }
});
