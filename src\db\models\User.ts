import mongoose from 'mongoose';
import bcrypt from 'bcrypt';
import { userSchema } from '@/db/schemas/user.schema';

/**
 * Custom time block interface
 */
export interface ICustomTimeBlock {
  startTime: string;
  endTime: string;
}

/**
 * User preferences interface
 */
export interface IUserPreferences {
  timeInterval: string;
  startHour: string;
  endHour: string;
  timeFormat: string;
  darkMode: boolean;
  syncEnabled: boolean;
  emailNotifications: boolean;
  customTimeBlocks?: ICustomTimeBlock[];
  useCustomTimeBlocks?: boolean;
}

/**
 * User document interface
 */
export interface IUser extends mongoose.Document {
  name: string;
  email: string;
  password: string;
  preferences: IUserPreferences;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

/**
 * User model methods interface
 */
interface IUserMethods {
  comparePassword(candidatePassword: string): Promise<boolean>;
}

/**
 * User model type
 */
type UserModel = mongoose.Model<IUser, {}, IUserMethods>;

/**
 * Compare password method
 */
userSchema.methods.comparePassword = async function(candidatePassword: string) {
  try {
    // Ensure we have a valid password to compare
    if (!candidatePassword) {
      return false;
    }

    // Explicitly cast this.password to string to satisfy TypeScript
    const passwordString = String(this.password);

    if (!passwordString || passwordString.length < 10) {
      return false;
    }

    // Ensure the candidate password is trimmed
    const trimmedPassword = candidatePassword.trim();

    if (trimmedPassword.length === 0) {
      return false;
    }

    // Use bcrypt to compare the password
    return await bcrypt.compare(trimmedPassword, passwordString);
  } catch (error) {
    // Return false instead of throwing to prevent login failures due to technical errors
    return false;
  }
};

/**
 * User model
 */
const User = (mongoose.models.User || mongoose.model<IUser, UserModel>('User', userSchema));

export default User;
